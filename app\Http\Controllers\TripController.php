<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreTripRequest;
use App\Http\Requests\UpdateTripRequest;
use App\Http\Resources\TripResource;
use App\Models\Trip;
use App\Repositories\TripRepository;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Support\Facades\Log;

class TripController extends Controller
{
    private TripRepository $repository;

    public function __construct(TripRepository $repository)
    {
        $this->repository = $repository;
        $this->authorizeResource(Trip::class, 'trip');
    }

    public function index(Request $request): AnonymousResourceCollection
    {
        // Check if search contains id_subs_type (relationship field)
        $search = $request->input('search', '');
        $hasSubsTypeSearch = strpos($search, 'id_subs_type:') !== false;

        if ($hasSubsTypeSearch) {
            // Handle custom search manually to avoid RequestCriteria conflicts
            $query = Trip::with(['line', 'startStation', 'endStation', 'tariffOptions'])
                         ->where('inter_station', false);

            $query = $this->repository->applyCustomSearch(
                $query,
                $search,
                $request->input('searchJoin', 'and')
            );

            return TripResource::collection(
                $query->latest()->paginate($request->input('perPage'))
            );
        } else {
            // Use standard repository with RequestCriteria for other searches
            return TripResource::collection(
                $this->repository
                    ->with(['line', 'startStation', 'endStation', 'tariffOptions'])
                    ->latest()->where('inter_station', false)
                    ->paginate($request->input('perPage'))
            );
        }
    }

    public function all(): AnonymousResourceCollection
    {
        return TripResource::collection($this->repository->all());
    }

    public function allNotInter(): AnonymousResourceCollection
    {
        return TripResource::collection($this->repository->where('inter_station', false)->get());
    }

    public function store(StoreTripRequest $request): JsonResponse
    {
        try {
            $data = $request->validated();

            // Check if a trip with the same route already exists
            $existingTrip = Trip::where('id_station_start', $data['stations']['id_station_start'])
                ->where('id_station_end', $data['stations']['id_station_end'])
                ->where('inter_station', false)
                ->first();

            if ($existingTrip) {
                // Check if any of the requested lines are already associated with this trip
                $existingLineIds = $existingTrip->lines()->pluck('lines.id')->toArray();
                $requestedLineIds = $data['line_ids'];
                $duplicateLineIds = array_intersect($existingLineIds, $requestedLineIds);

                if (!empty($duplicateLineIds)) {
                    return response()->json([
                        'message' => 'Trip_already_exists_with_some_lines',
                        'duplicate_line_ids' => $duplicateLineIds,
                        'existing_trip' => new TripResource($existingTrip->load(['lines', 'startStation', 'endStation', 'tariffOptions']))
                    ], 409);
                }

                // Add new lines to existing trip
                $newLineIds = array_diff($requestedLineIds, $existingLineIds);
                $existingTrip->lines()->attach($newLineIds);

                // Add new tariff options if they don't exist
                $existingSubsTypes = $existingTrip->tariffOptions()->pluck('id_subs_type')->toArray();
                foreach ($data['tariff_options'] as $option) {
                    if (!in_array($option['id_subs_type'], $existingSubsTypes)) {
                        $existingTrip->tariffOptions()->create([
                            'id_subs_type' => $option['id_subs_type'],
                            'is_regular' => $option['is_regular'],
                            'id_tariff_base' => $option['id_tariff_base'] ?? null,
                            'manual_tariff' => $option['manual_tariff'] ?? null
                        ]);
                    }
                }

                return response()->json([
                    'message' => 'Trip_updated_with_new_lines',
                    'data' => new TripResource($existingTrip->fresh()->load(['lines', 'startStation', 'endStation', 'tariffOptions']))
                ], 200);
            }

            // Create new trip
            $tripData = [
                'nom_fr' => $data['nom_fr'],
                'nom_en' => $data['nom_en'],
                'nom_ar' => $data['nom_ar'],
                'id_station_start' => $data['stations']['id_station_start'],
                'id_station_end' => $data['stations']['id_station_end'],
                'status' => $data['status'],
                'inter_station' => $data['inter_station'],
                'is_aller_retour' => $data['is_aller_retour'] ?? true,
                'number_of_km' => $data['number_of_km']
            ];

            $trip = $this->repository->create($tripData);

            // Attach lines to the trip
            $trip->lines()->attach($data['line_ids']);

            return response()->json([
                'message' => 'Trip_created_successfully',
                'data' => new TripResource($trip->load(['lines', 'startStation', 'endStation', 'tariffOptions']))
            ], 201);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed_to_create_trip',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    public function show(Trip $trip): TripResource
    {
        return new TripResource($trip->load(['line', 'startStation', 'endStation', 'tariffOptions']));
    }

    public function update(UpdateTripRequest $request, Trip $trip): JsonResponse
    {
        try {
            $data = $request->validated();

            if (isset($data['stations']) || isset($data['id_line'])) {
                $stationStart = $data['stations']['id_station_start'] ?? $trip->id_station_start;
                $stationEnd = $data['stations']['id_station_end'] ?? $trip->id_station_end;
                $lineId = $data['id_line'] ?? $trip->id_line;


                $isAllerRetour = $data['is_aller_retour'] ?? $trip->is_aller_retour;
                $existingTrip = Trip::where('id_line', $lineId)
                    ->where('id_station_start', $stationStart)
                    ->where('id_station_end', $stationEnd)
                    ->where('inter_station', false)
                    ->where('is_aller_retour', $isAllerRetour)
                    ->where('id', '!=', $trip->id)
                    ->first();

                if ($existingTrip) {
                    return response()->json([
                        'message' => 'Trip_already_exists',
                        'error' => 'Un trajet avec ces paramètres existe déjà.',
                        'existing_trip' => new TripResource($existingTrip)
                    ], 409);
                }

                $existingReverseTrip = Trip::where('id_line', $lineId)
                    ->where('id_station_start', $stationEnd)
                    ->where('id_station_end', $stationStart)
                    ->where('inter_station', false)
                    ->where('is_aller_retour', $isAllerRetour)
                    ->where('id', '!=', $trip->id)
                    ->first();

                if ($existingReverseTrip) {
                    return response()->json([
                        'message' => 'Reverse_trip_already_exists',
                        'error' => 'Un trajet inverse existe déjà pour cette ligne entre ces stations.',
                        'existing_trip' => new TripResource($existingReverseTrip),
                        'suggestion' => 'Vous pouvez utiliser le trajet existant en activant l\'option "trajet inversé".'
                    ], 409);
                }
            }

            $trip = $this->repository->update($data, $trip->id);

            return response()->json([
                'message' => 'Trip updated successfully',
                'data' => new TripResource($trip)
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to update trip',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    public function destroy(Trip $trip): JsonResponse
    {
        try {
            $this->repository->delete($trip->id);

            return response()->json([
                'message' => 'Trip deleted successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed_delete_trip',
                'error' => $e->getMessage()
            ], 500);
        }
    }


    public function checkTripExists(Request $request): JsonResponse
    {
        $request->validate([
            'id_line' => 'required|exists:lines,id',
            'id_station_start' => 'required|exists:stations,id',
            'id_station_end' => 'required|exists:stations,id|different:id_station_start'
        ]);

        $lineId = $request->input('id_line');
        $stationStart = $request->input('id_station_start');
        $stationEnd = $request->input('id_station_end');

        $directTrip = Trip::whereHas('lines', function($query) use ($lineId) {
                $query->where('lines.id', $lineId);
            })
            ->where('id_station_start', $stationStart)
            ->where('id_station_end', $stationEnd)
            ->where('inter_station', false)
            ->with(['lines', 'startStation', 'endStation', 'tariffOptions'])
            ->first();

        $reverseTrip = Trip::whereHas('lines', function($query) use ($lineId) {
                $query->where('lines.id', $lineId);
            })
            ->where('id_station_start', $stationEnd)
            ->where('id_station_end', $stationStart)
            ->where('inter_station', false)
            ->with(['lines', 'startStation', 'endStation', 'tariffOptions'])
            ->first();

        $result = [
            'direct_trip_exists' => !is_null($directTrip),
            'reverse_trip_exists' => !is_null($reverseTrip),
            'direct_trip' => $directTrip ? new TripResource($directTrip) : null,
            'reverse_trip' => $reverseTrip ? new TripResource($reverseTrip) : null
        ];

        if ($directTrip || $reverseTrip) {
            $result['message'] = 'Des trajets existent déjà entre ces stations';
            if ($reverseTrip && !$directTrip) {
                $result['suggestion'] = 'Vous pouvez utiliser le trajet inverse existant en activant l\'option "trajet inversé" lors de la création de l\'abonnement.';
            }
        } else {
            $result['message'] = 'Aucun trajet n\'existe entre ces stations sur cette ligne';
            $result['can_create'] = true;
        }

        return response()->json($result);
    }
}





<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Migrate existing trip-line relationships to the pivot table
        $trips = DB::table('trips')->whereNotNull('id_line')->get();

        foreach ($trips as $trip) {
            // Check if the relationship doesn't already exist
            $exists = DB::table('line_trip')
                ->where('line_id', $trip->id_line)
                ->where('trip_id', $trip->id)
                ->exists();

            if (!$exists) {
                DB::table('line_trip')->insert([
                    'line_id' => $trip->id_line,
                    'trip_id' => $trip->id,
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);
            }
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Remove all records from the pivot table
        DB::table('line_trip')->truncate();
    }
};

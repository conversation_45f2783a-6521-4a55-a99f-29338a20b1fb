<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class TripResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'nom_fr' => $this->nom_fr,
            'nom_en' => $this->nom_en,
            'nom_ar' => $this->nom_ar,
            'id_station_start' => $this->id_station_start,
            'id_station_end' => $this->id_station_end,
            'CODE_ABO_SCOL' => $this->CODE_ABO_SCOL,
            'CODE_ABO_CIVIL' => $this->CODE_ABO_CIVIL,
            'status' => $this->status,
            'inter_station' => $this->inter_station,
            'is_aller_retour' => $this->is_aller_retour,
            'number_of_km' => $this->number_of_km,
            'lines' => LineResource::collection($this->whenLoaded('lines')),
            'station_start' => new StationResource($this->whenLoaded('startStation')),
            'station_end' => new StationResource($this->whenLoaded('endStation')),
            'tariff_options' => TariffOptionResource::collection($this->whenLoaded('tariffOptions')),
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
}

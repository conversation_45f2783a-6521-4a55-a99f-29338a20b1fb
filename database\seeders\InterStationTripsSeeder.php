<?php

namespace Database\Seeders;

use App\Models\Line;
use App\Models\Station;
use App\Models\LineStation;
use App\Models\Trip;
use App\Models\WebsiteTrip;
use Illuminate\Database\Seeder;

class InterStationTripsSeeder extends Seeder
{
    private $createdWebsiteTrips = [];

    public function run(): void
    {
        // Get all lines
        $lines = Line::all();

        foreach ($lines as $line) {
            // Get all stations for this line ordered by position
            $lineStations = LineStation::where('id_line', $line->id)
                ->orderBy('position')
                ->get();

            // Create trips between consecutive stations
            for ($i = 0; $i < $lineStations->count() - 1; $i++) {
                $currentStation = $lineStations[$i];
                $nextStation = $lineStations[$i + 1];

                // Calculate estimated number of km (you might want to adjust this logic)
                $startStation = Station::find($currentStation->id_station);
                $endStation = Station::find($nextStation->id_station);

                // Get number_of_km from website_trips
                $websiteTrip = WebsiteTrip::where([
                    'id_line' => $line->id,
                    'id_station_start' => $currentStation->id_station,
                    'id_station_end' => $nextStation->id_station,
                ])->first();

                // Create website trip if not found
                if (!$websiteTrip) {
                    echo "No website trip found for: {$startStation->nom_fr} - {$endStation->nom_fr} (Line: {$line->CODE_LINE}), creating it...\n";
                    $websiteTrip = $this->createMissingWebsiteTrip($line, $currentStation->id_station, $nextStation->id_station, $startStation, $endStation);
                }

                // Create the trip
                $trip = Trip::create([
                    'nom_fr' => $startStation->nom_fr . ' - ' . $endStation->nom_fr,
                    'nom_en' => $startStation->nom_en . ' - ' . $endStation->nom_en,
                    'nom_ar' => $startStation->nom_ar . ' - ' . $endStation->nom_ar,
                    'id_station_start' => $currentStation->id_station,
                    'id_station_end' => $nextStation->id_station,
                    'CODE_ABO_SCOL' => [],
                    'CODE_ABO_CIVIL' => [],
                    'status' => true,
                    'is_aller_retour' => true,
                    'inter_station' => true,
                    'number_of_km' => $websiteTrip->number_of_km
                ]);

                // Attach the line to the trip using many-to-many relationship
                $trip->lines()->attach($line->id);
            }
        }

        // Display summary of created website trips
        $this->displaySummary();
    }

    private function createMissingWebsiteTrip($line, $startStationId, $endStationId, $startStation, $endStation): WebsiteTrip
    {
        // Calculate estimated distance (you can adjust this logic)
        $estimatedDistance = $this->calculateEstimatedDistance($startStation, $endStation);

        // Generate a unique ID for the website trip
        $maxId = WebsiteTrip::max('id') ?? 0;
        $newId = $maxId + 1;

        // Create the missing website trip
        $websiteTrip = WebsiteTrip::create([
            'id' => $newId,
            'code' => "{$startStation->nom_fr} - {$endStation->nom_fr}",
            'id_line' => $line->id,
            'id_station_start' => $startStationId,
            'id_station_end' => $endStationId,
            'number_of_km' => $estimatedDistance,
            'status' => true,
        ]);

        // Track that we created this website trip
        $this->createdWebsiteTrips[] = [
            'id' => $newId,
            'route' => "{$startStation->nom_fr} - {$endStation->nom_fr}",
            'line' => $line->CODE_LINE,
            'distance' => $estimatedDistance
        ];

        echo "Created missing website trip: {$startStation->nom_fr} - {$endStation->nom_fr} (Line: {$line->CODE_LINE})\n";

        return $websiteTrip;
    }

    private function calculateEstimatedDistance($startStation, $endStation): float
    {
        // If both stations have coordinates, calculate distance
        if ($startStation->latitude && $startStation->longitude &&
            $endStation->latitude && $endStation->longitude) {

            // Simple distance calculation (you can use a more sophisticated formula)
            $latDiff = abs($startStation->latitude - $endStation->latitude);
            $lonDiff = abs($startStation->longitude - $endStation->longitude);

            // Rough estimation: 1 degree ≈ 111 km
            $distance = sqrt(pow($latDiff * 111, 2) + pow($lonDiff * 111, 2));

            return round($distance, 2);
        }

        // Default distance if coordinates are not available
        return 5.0; // Default 5 km between consecutive stations
    }

    private function displaySummary(): void
    {
        if (!empty($this->createdWebsiteTrips)) {
            echo "\n=== MISSING WEBSITE TRIPS CREATED ===\n";
            foreach ($this->createdWebsiteTrips as $trip) {
                echo "✓ Created website trip ID {$trip['id']}: {$trip['route']} (Line: {$trip['line']}, Distance: {$trip['distance']} km)\n";
            }
            echo "Total missing website trips created: " . count($this->createdWebsiteTrips) . "\n";
            echo "======================================\n\n";
        } else {
            echo "\n✅ No missing website trips found. All inter-station trips created successfully!\n\n";
        }
    }
}



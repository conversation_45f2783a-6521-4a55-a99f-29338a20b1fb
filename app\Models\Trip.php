<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use OwenIt\Auditing\Contracts\Auditable;

class Trip extends Model implements Auditable
{
    use \OwenIt\Auditing\Auditable;
    protected $fillable = [
        'nom_fr',
        'nom_en',
        'nom_ar',
        'id_station_start',
        'id_station_end',
        'CODE_ABO_SCOL',
        'CODE_ABO_CIVIL',
        'status',
        'inter_station',
        'is_aller_retour',
        'number_of_km'
    ];

    protected $casts = [
        'status' => 'boolean',
        'inter_station' => 'boolean',
        'is_aller_retour' => 'boolean',
        'number_of_km' => 'integer',
        'CODE_ABO_SCOL' => 'array',
        'CODE_ABO_CIVIL' => 'array'
    ];

    public function lines(): BelongsToMany
    {
        return $this->belongsToMany(Line::class, 'line_trip');
    }

    public function startStation(): BelongsTo
    {
        return $this->belongsTo(Station::class, 'id_station_start');
    }

    public function endStation(): BelongsTo
    {
        return $this->belongsTo(Station::class, 'id_station_end');
    }

    public function tariffOptions(): HasMany
    {
        return $this->hasMany(TariffOption::class, 'id_trip');
    }
}



